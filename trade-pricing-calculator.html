<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>外贸智能动态定价与税务计算器</title>
    <!-- 引入Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- 引入Font Awesome图标 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            max-width: 1200px;
            padding: 30px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #4a5568;
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1rem;
            color: #718096;
        }
        
        .input-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        
        .result-section {
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        
        .pricing-result {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
        }
        
        .tax-result {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
            color: white;
        }
        
        .profit-result {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }
        
        .form-label {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e2e8f0;
            padding: 12px 15px;
            font-size: 1rem;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .result-card {
            text-align: center;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 15px;
        }
        
        .result-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .result-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .highlight-box {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }
        
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .result-value {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- 页面标题 -->
            <div class="header">
                <h1><i class="fas fa-calculator"></i> 外贸智能动态定价与税务计算器</h1>
                <p>基于实际业务公式的精确计算</p>
            </div>
            
            <div class="row">
                <!-- 左侧参数设置区域 -->
                <div class="col-lg-5">
                    <div class="input-section">
                        <h4><i class="fas fa-cog"></i> 参数设置</h4>
                        
                        <!-- 基础费用输入 -->
                        <div class="mb-3">
                            <label class="form-label">运输费用 (T) - 元:</label>
                            <input type="number" class="form-control" id="transportCost" value="4000" min="0" step="0.01">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">人工成本 (L) - 元:</label>
                            <input type="number" class="form-control" id="laborCost" value="1000" min="0" step="0.01">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">进货数量 (Q) - 件:</label>
                            <input type="number" class="form-control" id="quantity" value="500" min="1" step="1">
                        </div>
                        
                        <!-- 核心参数输入 -->
                        <div class="mb-3">
                            <label class="form-label">销售单价 (P) - 元:</label>
                            <input type="number" class="form-control" id="salesPrice" value="200" min="0" step="0.01">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">进货单价 (C) - 元:</label>
                            <input type="number" class="form-control" id="unitCost" value="100" min="0" step="0.01">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">目标利润率 (MR) - %:</label>
                            <input type="number" class="form-control" id="targetProfit" value="52" min="0" max="200" step="0.1">
                        </div>
                        
                        <!-- 汇率输入 -->
                        <div class="mb-3">
                            <label class="form-label">汇率 (EUR/CNY):</label>
                            <input type="number" class="form-control" id="exchangeRate" value="7.5" min="0" step="0.01">
                        </div>
                        
                        <!-- 业务类型选择 -->
                        <div class="mb-3">
                            <label class="form-label">业务类型:</label>
                            <select class="form-select" id="businessType">
                                <option value="export">出口业务 (增值税0%)</option>
                                <option value="import">进口业务 (增值税13%)</option>
                                <option value="domestic">国内贸易 (增值税13%)</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧结果显示区域 -->
                <div class="col-lg-7">
                    <!-- 基础计算结果 -->
                    <div class="result-section pricing-result">
                        <h4><i class="fas fa-chart-line"></i> 基础计算结果:</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="highlight-box">
                                    <div class="result-label">进货成本 (IC)</div>
                                    <div class="result-value" id="importCost">¥ 50,000.00</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="highlight-box">
                                    <div class="result-label">总成本 (TC)</div>
                                    <div class="result-value" id="totalCost">¥ 55,000.00</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="highlight-box">
                                    <div class="result-label">销售额 (SA)</div>
                                    <div class="result-value" id="salesAmount">¥ 100,000.00</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="highlight-box">
                                    <div class="result-label">实际利润率 (AR)</div>
                                    <div class="result-value" id="actualProfitRate" style="color: #90EE90;">81.8%</div>
                                </div>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <div class="highlight-box">
                                <div class="result-label">建议销售额 (SA_suggested)</div>
                                <div class="result-value" id="suggestedSalesAmount">¥ 83,600.00</div>
                                <div style="font-size: 0.9rem; margin-top: 5px;">欧元: <span id="suggestedSalesAmountEUR">€ 11,146.67</span></div>
                            </div>
                        </div>
                    </div>

                    <!-- 税务计算结果 -->
                    <div class="result-section tax-result">
                        <h4><i class="fas fa-receipt"></i> 税务计算结果:</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="highlight-box">
                                    <div class="result-label">增值税 (VAT)</div>
                                    <div class="result-value" id="vatAmount">¥ 0.00</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="highlight-box">
                                    <div class="result-label">附加税 (Surcharge)</div>
                                    <div class="result-value" id="additionalTax">¥ 0.00</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="highlight-box">
                                    <div class="result-label">印花税 (Stamp Duty)</div>
                                    <div class="result-value" id="stampTax">¥ 300.00</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="highlight-box">
                                    <div class="result-label">企业所得税 (Enterprise Tax)</div>
                                    <div class="result-value" id="corporateTax">¥ 11,175.00</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 税后净利润 -->
                    <div class="result-section profit-result">
                        <div class="text-center">
                            <h4><i class="fas fa-coins"></i> 税后净利润</h4>
                            <div class="result-value" id="netProfit" style="font-size: 3rem;">¥ 33,525.00</div>
                            <div style="font-size: 1.2rem; margin-top: 10px;">
                                利润差距: <span id="profitGap">+29.8%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // 获取DOM元素
        const elements = {
            transportCost: document.getElementById('transportCost'),
            laborCost: document.getElementById('laborCost'),
            quantity: document.getElementById('quantity'),
            salesPrice: document.getElementById('salesPrice'),
            unitCost: document.getElementById('unitCost'),
            targetProfit: document.getElementById('targetProfit'),
            exchangeRate: document.getElementById('exchangeRate'),
            businessType: document.getElementById('businessType'),

            // 结果显示元素
            importCost: document.getElementById('importCost'),
            totalCost: document.getElementById('totalCost'),
            salesAmount: document.getElementById('salesAmount'),
            actualProfitRate: document.getElementById('actualProfitRate'),
            suggestedSalesAmount: document.getElementById('suggestedSalesAmount'),
            suggestedSalesAmountEUR: document.getElementById('suggestedSalesAmountEUR'),

            vatAmount: document.getElementById('vatAmount'),
            additionalTax: document.getElementById('additionalTax'),
            stampTax: document.getElementById('stampTax'),
            corporateTax: document.getElementById('corporateTax'),
            netProfit: document.getElementById('netProfit'),
            profitGap: document.getElementById('profitGap')
        };

        // 格式化货币显示
        function formatCurrency(amount, currency = '¥') {
            return `${currency} ${amount.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
        }

        // 格式化欧元显示
        function formatEUR(amount) {
            return `€ ${amount.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
        }

        // 主计算函数 - 严格按照业务公式
        function calculateAll() {
            // 获取输入值
            const T = parseFloat(elements.transportCost.value) || 0;  // 运输费用
            const L = parseFloat(elements.laborCost.value) || 0;      // 人工成本
            const Q = parseInt(elements.quantity.value) || 1;         // 进货数量
            const P = parseFloat(elements.salesPrice.value) || 0;     // 销售单价
            const C = parseFloat(elements.unitCost.value) || 0;       // 进货单价
            const MR = parseFloat(elements.targetProfit.value) || 0;  // 目标利润率
            const exchangeRate = parseFloat(elements.exchangeRate.value) || 7.5;
            const businessType = elements.businessType.value;

            // 1. 进货成本 (IC) = 进货单价(C) × 进货数量(Q)
            const IC = C * Q;

            // 2. 总成本 (TC) = 进货成本(IC) + 运输费用(T) + 人工成本(L)
            const TC = IC + T + L;

            // 3. 销售额 (SA) = 销售单价(P) × 销售数量(Q)
            const SA = P * Q;

            // 4. 实际利润 = 销售额 - 总成本
            const actualProfit = SA - TC;

            // 5. 实际利润率 (AR) = (实际利润 / 总成本) × 100
            const AR = TC > 0 ? (actualProfit / TC) * 100 : 0;

            // 6. 建议销售额 (SA_suggested) = 总成本 × (1 + 目标利润率/100) / 汇率
            const SA_suggested = TC * (1 + MR / 100);
            const SA_suggested_EUR = SA_suggested / exchangeRate;

            // 7. 税务计算
            let vatRate = 0;
            if (businessType === 'export') {
                vatRate = 0;      // 出口业务免增值税
            } else {
                vatRate = 0.13;   // 进口业务和国内贸易13%
            }

            // 7.1 增值税 (VAT) = 销售额(SA) × 增值税率
            const VAT = SA * vatRate;

            // 7.2 附加税 (Surcharge) = 增值税(VAT) × 12%
            const surcharge = VAT * 0.12;

            // 7.3 印花税 (Stamp Duty) = 销售额(SA) × 0.0003
            const stampDuty = SA * 0.0003;

            // 7.4 企业所得税 = max(实际利润 - 增值税 - 附加税 - 印花税, 0) × 25%
            const taxableIncome = Math.max(actualProfit - VAT - surcharge - stampDuty, 0);
            const enterpriseTax = taxableIncome * 0.25;

            // 7.5 税后净利润 = 实际利润 - 增值税 - 附加税 - 印花税 - 企业所得税
            const netProfitAfterTax = actualProfit - VAT - surcharge - stampDuty - enterpriseTax;

            // 8. 利润差距 = 实际利润率 - 目标利润率
            const profitGap = AR - MR;

            // 更新界面显示
            elements.importCost.textContent = formatCurrency(IC);
            elements.totalCost.textContent = formatCurrency(TC);
            elements.salesAmount.textContent = formatCurrency(SA);
            elements.suggestedSalesAmount.textContent = formatCurrency(SA_suggested);
            elements.suggestedSalesAmountEUR.textContent = formatEUR(SA_suggested_EUR);

            // 利润率状态判断和显示
            let profitStatus = '';
            let profitColor = '';

            if (profitGap >= 10) {
                profitStatus = ' (优秀)';
                profitColor = '#90EE90';
            } else if (profitGap >= 0) {
                profitStatus = ' (良好)';
                profitColor = '#98FB98';
            } else if (profitGap >= -5) {
                profitStatus = ' (接近目标)';
                profitColor = '#FFD700';
            } else {
                profitStatus = ' (需要调整)';
                profitColor = '#FFA07A';
            }

            elements.actualProfitRate.innerHTML = `${AR.toFixed(1)}%${profitStatus}`;
            elements.actualProfitRate.style.color = profitColor;

            // 更新税务结果显示
            elements.vatAmount.textContent = formatCurrency(VAT);
            elements.additionalTax.textContent = formatCurrency(surcharge);
            elements.stampTax.textContent = formatCurrency(stampDuty);
            elements.corporateTax.textContent = formatCurrency(enterpriseTax);
            elements.netProfit.textContent = formatCurrency(netProfitAfterTax);
            elements.profitGap.textContent = `${profitGap >= 0 ? '+' : ''}${profitGap.toFixed(1)}%`;
        }

        // 绑定事件监听器
        function bindEvents() {
            // 所有输入框都绑定input事件
            elements.transportCost.addEventListener('input', calculateAll);
            elements.laborCost.addEventListener('input', calculateAll);
            elements.quantity.addEventListener('input', calculateAll);
            elements.salesPrice.addEventListener('input', calculateAll);
            elements.unitCost.addEventListener('input', calculateAll);
            elements.targetProfit.addEventListener('input', calculateAll);
            elements.exchangeRate.addEventListener('input', calculateAll);
            elements.businessType.addEventListener('change', calculateAll);
        }

        // 初始化
        function init() {
            bindEvents();
            calculateAll();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
