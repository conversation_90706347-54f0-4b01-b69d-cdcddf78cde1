<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>外贸智能动态定价与税务计算器</title>
    <!-- 引入Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- 引入Font Awesome图标 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            max-width: 1200px;
            padding: 30px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #4a5568;
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1rem;
            color: #718096;
        }
        
        .input-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        
        .result-section {
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        
        .pricing-result {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
        }
        
        .tax-result {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
            color: white;
        }
        
        .profit-result {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }
        
        .form-label {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e2e8f0;
            padding: 12px 15px;
            font-size: 1rem;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .slider-container {
            margin: 15px 0;
        }
        
        .range-slider {
            width: 100%;
            height: 8px;
            border-radius: 5px;
            background: #e2e8f0;
            outline: none;
            -webkit-appearance: none;
        }
        
        .range-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
        }
        
        .range-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            border: none;
        }
        
        .result-card {
            text-align: center;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 15px;
        }
        
        .result-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .result-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .highlight-box {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }
        
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .result-value {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- 页面标题 -->
            <div class="header">
                <h1><i class="fas fa-calculator"></i> 外贸智能动态定价与税务计算器</h1>
                <p>实时计算定价、税务和利润</p>
            </div>
            
            <div class="row">
                <!-- 左侧参数设置区域 -->
                <div class="col-lg-5">
                    <div class="input-section">
                        <h4><i class="fas fa-cog"></i> 参数设置</h4>
                        
                        <!-- 基础费用输入 -->
                        <div class="mb-3">
                            <label class="form-label">运输费用 (元):</label>
                            <input type="number" class="form-control" id="shippingCost" value="4000" min="0" step="0.01">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">人工成本 (元):</label>
                            <input type="number" class="form-control" id="laborCost" value="1000" min="0" step="0.01">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">进货数量 (件):</label>
                            <input type="number" class="form-control" id="quantity" value="500" min="1" step="1">
                        </div>
                        
                        <!-- 订单销售额滑块 -->
                        <div class="mb-3">
                            <label class="form-label">订单销售额 (€): <span id="salesAmountDisplay">15000</span></label>
                            <div class="slider-container">
                                <input type="range" class="range-slider" id="salesAmount" min="1000" max="100000" value="15000" step="100">
                            </div>
                        </div>
                        
                        <!-- 进货单价滑块 -->
                        <div class="mb-3">
                            <label class="form-label">进货单价 (元): <span id="unitCostDisplay">100</span></label>
                            <div class="slider-container">
                                <input type="range" class="range-slider" id="unitCost" min="10" max="1000" value="100" step="1">
                            </div>
                        </div>
                        
                        <!-- 目标利润率滑块 -->
                        <div class="mb-3">
                            <label class="form-label">目标利润率 (%): <span id="targetProfitDisplay">52</span></label>
                            <div class="slider-container">
                                <input type="range" class="range-slider" id="targetProfit" min="0" max="100" value="52" step="0.1">
                            </div>
                        </div>
                        
                        <!-- 业务类型选择 -->
                        <div class="mb-3">
                            <label class="form-label">业务类型:</label>
                            <select class="form-select" id="businessType">
                                <option value="export">出口业务</option>
                                <option value="import">进口业务</option>
                                <option value="domestic">国内贸易</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧结果显示区域 -->
                <div class="col-lg-7">
                    <!-- 定价计算结果 -->
                    <div class="result-section pricing-result">
                        <h4><i class="fas fa-chart-line"></i> 定价计算结果:</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="highlight-box">
                                    <div class="result-label">当前销售额</div>
                                    <div class="result-value" id="currentSalesAmount">€ 15000.00</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="highlight-box">
                                    <div class="result-label">建议销售额</div>
                                    <div class="result-value" id="suggestedSalesAmount">€ 11146.67</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="highlight-box">
                                    <div class="result-label">进货单价</div>
                                    <div class="result-value" id="displayUnitCost">€ 13.33</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="highlight-box">
                                    <div class="result-label">目标利润率</div>
                                    <div class="result-value" id="displayTargetProfit">52.0%</div>
                                </div>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <div class="highlight-box">
                                <div class="result-label">实际利润率</div>
                                <div class="result-value" id="actualProfitRate" style="color: #90EE90;">104.5% (优秀 (超出目标10%+))</div>
                                <div style="font-size: 0.9rem; margin-top: 5px;">利润率差距: <span id="profitGap">+52.5%</span></div>
                            </div>
                        </div>
                    </div>

                    <!-- 税务计算结果 -->
                    <div class="result-section tax-result">
                        <h4><i class="fas fa-receipt"></i> 税务计算结果:</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="highlight-box">
                                    <div class="result-label">增值税</div>
                                    <div class="result-value" id="vatAmount">¥ 0.00</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="highlight-box">
                                    <div class="result-label">附加税</div>
                                    <div class="result-value" id="additionalTax">¥ 0.00</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="highlight-box">
                                    <div class="result-label">印花税</div>
                                    <div class="result-value" id="stampTax">¥ 33.75</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="highlight-box">
                                    <div class="result-label">企业所得税</div>
                                    <div class="result-value" id="corporateTax">¥ 14366.56</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 税后净利润 -->
                    <div class="result-section profit-result">
                        <div class="text-center">
                            <h4><i class="fas fa-coins"></i> 税后净利润</h4>
                            <div class="result-value" id="netProfit" style="font-size: 3rem;">¥ 43099.69</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // 汇率常量 (EUR to CNY)
        const EUR_TO_CNY = 7.5;

        // 税率常量
        const TAX_RATES = {
            VAT: 0.13,           // 增值税率 13%
            ADDITIONAL: 0.12,    // 附加税率 12%
            STAMP: 0.0003,       // 印花税率 0.03%
            CORPORATE: 0.25      // 企业所得税率 25%
        };

        // 获取DOM元素
        const elements = {
            shippingCost: document.getElementById('shippingCost'),
            laborCost: document.getElementById('laborCost'),
            quantity: document.getElementById('quantity'),
            salesAmount: document.getElementById('salesAmount'),
            salesAmountDisplay: document.getElementById('salesAmountDisplay'),
            unitCost: document.getElementById('unitCost'),
            unitCostDisplay: document.getElementById('unitCostDisplay'),
            targetProfit: document.getElementById('targetProfit'),
            targetProfitDisplay: document.getElementById('targetProfitDisplay'),
            businessType: document.getElementById('businessType'),

            // 结果显示元素
            currentSalesAmount: document.getElementById('currentSalesAmount'),
            suggestedSalesAmount: document.getElementById('suggestedSalesAmount'),
            displayUnitCost: document.getElementById('displayUnitCost'),
            displayTargetProfit: document.getElementById('displayTargetProfit'),
            actualProfitRate: document.getElementById('actualProfitRate'),
            profitGap: document.getElementById('profitGap'),

            vatAmount: document.getElementById('vatAmount'),
            additionalTax: document.getElementById('additionalTax'),
            stampTax: document.getElementById('stampTax'),
            corporateTax: document.getElementById('corporateTax'),
            netProfit: document.getElementById('netProfit')
        };

        // 格式化货币显示
        function formatCurrency(amount, currency = '¥') {
            return `${currency} ${amount.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
        }

        // 格式化欧元显示
        function formatEUR(amount) {
            return `€ ${amount.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
        }

        // 计算所有数值
        function calculateAll() {
            // 获取输入值
            const shippingCost = parseFloat(elements.shippingCost.value) || 0;
            const laborCost = parseFloat(elements.laborCost.value) || 0;
            const quantity = parseInt(elements.quantity.value) || 1;
            const salesAmountEUR = parseFloat(elements.salesAmount.value) || 0;
            const unitCostCNY = parseFloat(elements.unitCost.value) || 0;
            const targetProfitRate = parseFloat(elements.targetProfit.value) || 0;

            // 更新滑块显示值
            elements.salesAmountDisplay.textContent = salesAmountEUR.toLocaleString();
            elements.unitCostDisplay.textContent = unitCostCNY.toLocaleString();
            elements.targetProfitDisplay.textContent = targetProfitRate.toFixed(1);

            // 基础计算
            const salesAmountCNY = salesAmountEUR * EUR_TO_CNY;
            const totalCost = (unitCostCNY * quantity) + shippingCost + laborCost;
            const unitCostEUR = unitCostCNY / EUR_TO_CNY;

            // 计算实际利润率
            const grossProfit = salesAmountCNY - totalCost;
            const actualProfitRate = totalCost > 0 ? (grossProfit / totalCost) * 100 : 0;

            // 计算建议销售额（基于目标利润率）
            const suggestedSalesAmountCNY = totalCost * (1 + targetProfitRate / 100);
            const suggestedSalesAmountEUR = suggestedSalesAmountCNY / EUR_TO_CNY;

            // 更新定价结果显示
            elements.currentSalesAmount.textContent = formatEUR(salesAmountEUR);
            elements.suggestedSalesAmount.textContent = formatEUR(suggestedSalesAmountEUR);
            elements.displayUnitCost.textContent = formatEUR(unitCostEUR);
            elements.displayTargetProfit.textContent = `${targetProfitRate.toFixed(1)}%`;

            // 利润率状态判断和显示
            const profitGap = actualProfitRate - targetProfitRate;
            let profitStatus = '';
            let profitColor = '';

            if (profitGap >= 10) {
                profitStatus = '(优秀 (超出目标10%+))';
                profitColor = '#90EE90';
            } else if (profitGap >= 0) {
                profitStatus = '(良好 (达到目标))';
                profitColor = '#98FB98';
            } else if (profitGap >= -5) {
                profitStatus = '(接近目标)';
                profitColor = '#FFD700';
            } else {
                profitStatus = '(需要调整)';
                profitColor = '#FFA07A';
            }

            elements.actualProfitRate.innerHTML = `${actualProfitRate.toFixed(1)}% ${profitStatus}`;
            elements.actualProfitRate.style.color = profitColor;
            elements.profitGap.textContent = `${profitGap >= 0 ? '+' : ''}${profitGap.toFixed(1)}%`;

            // 税务计算
            const vatBase = salesAmountCNY;
            const vatAmount = vatBase * TAX_RATES.VAT;
            const additionalTaxAmount = vatAmount * TAX_RATES.ADDITIONAL;
            const stampTaxAmount = salesAmountCNY * TAX_RATES.STAMP;
            const taxableIncome = Math.max(0, grossProfit - vatAmount - additionalTaxAmount - stampTaxAmount);
            const corporateTaxAmount = taxableIncome * TAX_RATES.CORPORATE;

            // 计算税后净利润
            const netProfitAmount = grossProfit - vatAmount - additionalTaxAmount - stampTaxAmount - corporateTaxAmount;

            // 更新税务结果显示
            elements.vatAmount.textContent = formatCurrency(vatAmount);
            elements.additionalTax.textContent = formatCurrency(additionalTaxAmount);
            elements.stampTax.textContent = formatCurrency(stampTaxAmount);
            elements.corporateTax.textContent = formatCurrency(corporateTaxAmount);
            elements.netProfit.textContent = formatCurrency(netProfitAmount);
        }

        // 绑定事件监听器
        function bindEvents() {
            // 输入框事件
            elements.shippingCost.addEventListener('input', calculateAll);
            elements.laborCost.addEventListener('input', calculateAll);
            elements.quantity.addEventListener('input', calculateAll);
            elements.businessType.addEventListener('change', calculateAll);

            // 滑块事件
            elements.salesAmount.addEventListener('input', calculateAll);
            elements.unitCost.addEventListener('input', calculateAll);
            elements.targetProfit.addEventListener('input', calculateAll);
        }

        // 初始化
        function init() {
            bindEvents();
            calculateAll();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
