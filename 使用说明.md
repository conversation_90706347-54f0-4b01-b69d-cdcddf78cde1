# 外贸智能动态定价与税务计算器 - 使用说明

## 概述

这是一个专为外贸业务设计的智能定价计算器，能够实时计算定价、税务和利润，帮助外贸从业者快速准确地进行报价决策。

## 功能特点

### 🎯 核心功能
- **实时计算**：所有数值变化立即响应，无需手动刷新
- **双向调整**：支持通过调整利润率自动计算建议售价，或通过调整售价查看利润率
- **税务计算**：自动计算增值税、附加税、印花税和企业所得税
- **利润分析**：智能判断利润率状态，提供优化建议

### 📱 技术特点
- **单文件设计**：所有功能集成在一个HTML文件中，便于分享和使用
- **响应式设计**：完美支持桌面端和移动端设备
- **无需安装**：直接在浏览器中打开即可使用
- **离线可用**：除了CDN资源外，无需网络连接

## 界面说明

### 左侧参数设置区域

#### 基础费用输入
- **运输费用 (元)**：货物运输相关的所有费用
- **人工成本 (元)**：人力成本，包括包装、装卸等
- **进货数量 (件)**：本次交易的商品数量

#### 滑块调节参数
- **订单销售额 (€)**：预期的销售总额（欧元）
  - 范围：1,000 - 100,000 欧元
  - 步长：100 欧元
  
- **进货单价 (元)**：单个商品的采购成本（人民币）
  - 范围：10 - 1,000 元
  - 步长：1 元
  
- **目标利润率 (%)**：期望达到的利润率
  - 范围：0% - 100%
  - 步长：0.1%

#### 业务类型选择
- **出口业务**：适用于出口贸易
- **进口业务**：适用于进口贸易  
- **国内贸易**：适用于国内贸易

### 右侧结果显示区域

#### 定价计算结果（蓝色区域）
- **当前销售额**：用户设定的销售额
- **建议销售额**：基于目标利润率计算的建议销售额
- **进货单价**：显示欧元换算后的进货单价
- **目标利润率**：用户设定的目标利润率
- **实际利润率**：基于当前参数计算的实际利润率
  - 🟢 优秀：超出目标10%以上
  - 🟢 良好：达到目标范围
  - 🟡 接近目标：差距在5%以内
  - 🟠 需要调整：低于目标5%以上

#### 税务计算结果（橙色区域）
- **增值税**：按13%税率计算
- **附加税**：按增值税的12%计算
- **印花税**：按销售额的0.03%计算
- **企业所得税**：按应税所得的25%计算

#### 税后净利润（绿色区域）
显示扣除所有税费后的最终净利润

## 使用步骤

### 1. 基础信息输入
1. 在左侧输入运输费用、人工成本和进货数量
2. 选择适合的业务类型

### 2. 参数调节
1. 使用滑块调节订单销售额到预期值
2. 设置进货单价
3. 设置目标利润率

### 3. 结果分析
1. 查看右侧的实时计算结果
2. 根据利润率状态判断是否需要调整
3. 参考建议销售额进行报价决策

### 4. 优化调整
1. 如果实际利润率不理想，可以：
   - 调整销售额滑块
   - 修改目标利润率
   - 优化成本结构
2. 实时观察各项指标的变化

## 计算公式说明

### 基础计算
- **总成本** = 进货单价 × 数量 + 运输费用 + 人工成本
- **毛利润** = 销售额（人民币） - 总成本
- **实际利润率** = (毛利润 ÷ 总成本) × 100%
- **建议销售额** = 总成本 × (1 + 目标利润率)

### 税务计算
- **增值税** = 销售额 × 13%
- **附加税** = 增值税 × 12%
- **印花税** = 销售额 × 0.03%
- **应税所得** = 毛利润 - 增值税 - 附加税 - 印花税
- **企业所得税** = 应税所得 × 25%
- **税后净利润** = 毛利润 - 所有税费

### 汇率换算
- 默认汇率：1 EUR = 7.5 CNY
- 可根据实际汇率在代码中调整 `EUR_TO_CNY` 常量

## 使用技巧

### 💡 定价策略
1. **保守策略**：设置较低的目标利润率，确保竞争力
2. **积极策略**：设置较高的目标利润率，追求最大收益
3. **平衡策略**：根据市场情况动态调整

### 📊 数据分析
1. 观察利润率差距，了解定价合理性
2. 比较建议销售额与市场价格
3. 分析税务成本对最终收益的影响

### 🔄 实时调整
1. 利用滑块快速测试不同参数组合
2. 实时观察利润率状态变化
3. 找到最优的价格平衡点

## 注意事项

### ⚠️ 重要提醒
1. **汇率变动**：实际使用时请根据当日汇率调整
2. **税率更新**：税率可能因政策变化而调整，请及时更新
3. **成本核算**：确保所有成本项目都已包含在内
4. **市场因素**：计算结果仅供参考，还需考虑市场竞争等因素

### 🛠️ 技术要求
- 现代浏览器（Chrome、Firefox、Safari、Edge等）
- 支持JavaScript
- 网络连接（仅用于加载CDN资源）

## 常见问题

### Q: 如何修改汇率？
A: 在HTML文件中找到 `EUR_TO_CNY = 7.5` 这行代码，修改数值即可。

### Q: 如何调整税率？
A: 在 `TAX_RATES` 对象中修改相应的税率数值。

### Q: 为什么利润率显示为负数？
A: 这表示当前定价下会出现亏损，需要提高销售价格或降低成本。

### Q: 如何保存计算结果？
A: 可以截图保存，或者复制相关数值到其他文档中。

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 实现基础定价计算功能
- 添加税务计算模块
- 支持响应式设计

---

**开发者**: AI Assistant  
**技术支持**: 如有问题请联系开发团队  
**最后更新**: 2024年1月
